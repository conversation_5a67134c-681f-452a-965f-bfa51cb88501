using UnityEngine;
using System;

namespace ParticleSystemToGfxFramework.Data
{
    /// <summary>
    /// 粒子系统验证配置，支持配置化的验证规则
    /// </summary>
    [CreateAssetMenu(fileName = "ConversionRules", menuName = "ParticleToGfx/Validation Config")]
    public class ValidationConfig : ScriptableObject
    {
        [Header("配置信息")]
        [SerializeField] private string version = "1.0";
        [SerializeField] private string description = "粒子系统转换验证规则配置";
        
        [Header("验证规则")]
        [SerializeField] private ExpressionRule[] rules = new ExpressionRule[0];
        
        /// <summary>
        /// 配置版本
        /// </summary>
        public string Version => version;
        
        /// <summary>
        /// 配置描述
        /// </summary>
        public string Description => description;
        
        /// <summary>
        /// 验证规则
        /// </summary>
        public ExpressionRule[] Rules => rules;
        
        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        public bool IsValid()
        {
            // 检查是否至少有一个验证规则
            return rules.Length > 0;
        }

        /// <summary>
        /// 获取所有规则的总数
        /// </summary>
        public int GetTotalRuleCount()
        {
            return rules.Length;
        }
        
        /// <summary>
        /// 创建默认配置
        /// </summary>
        public static ValidationConfig CreateDefault()
        {
            var config = CreateInstance<ValidationConfig>();
            config.version = "1.0";
            config.description = "默认粒子系统转换验证规则";

            // 统一使用表达式规则
            config.rules = new ExpressionRule[]
            {
                new ExpressionRule
                {
                    name = "maxParticles",
                    description = "检查粒子最大数量必须为1",
                    expression = "main.maxParticles == 1",
                    severity = ValidationSeverity.Error,
                    errorMessage = "Max Particles必须等于1"
                },
                new ExpressionRule
                {
                    name = "renderModeAndMesh",
                    description = "检查渲染模式必须为Mesh且Mesh不为空",
                    expression = "renderer.renderMode == 4 && renderer.mesh != null",
                    severity = ValidationSeverity.Error,
                    errorMessage = "必须使用Mesh渲染模式且Mesh不为空"
                },
                new ExpressionRule
                {
                    name = "forbiddenModules",
                    description = "检查禁用模块未启用",
                    expression = "!shape.enabled && !velocityOverLifetime.enabled && !limitVelocityOverLifetime.enabled && !inheritVelocity.enabled && !forceOverLifetime.enabled && !colorBySpeed.enabled && !sizeBySpeed.enabled && !rotationBySpeed.enabled && !externalForces.enabled && !noise.enabled && !collision.enabled && !trigger.enabled && !subEmitters.enabled && !textureSheetAnimation.enabled && !lights.enabled && !trails.enabled",
                    severity = ValidationSeverity.Error,
                    errorMessage = "不能启用以下禁用模块: Shape, Velocity over Lifetime, Limit Velocity over Lifetime, Inherit Velocity, Force over Lifetime, Color by Speed, Size by Speed, Rotation by Speed, External Forces, Noise, Collision, Triggers, Sub Emitters, Texture Sheet Animation, Lights, Trails"
                }
            };

            return config;
        }
    }
}
