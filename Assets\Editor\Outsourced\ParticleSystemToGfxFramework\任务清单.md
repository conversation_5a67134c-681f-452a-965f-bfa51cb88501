# ParticleSystemToGfxFramework 开发任务清单

## 📋 项目概述

基于用户指定的开发流程，将开发任务按照实际转换步骤进行分解，确保每个步骤完成后都可以独立测试，便于尽早发现问题。

## 🎯 开发策略

- **流程驱动**：按照实际转换流程顺序开发
- **步骤可测**：每完成一个步骤都能独立测试验证
- **增量实现**：从最基础的功能开始，逐步增加复杂度
- **快速反馈**：每个步骤都有明确的测试标准

## 🔄 用户指定的开发流程

1. **创建执行入口** → Unity编辑器右键菜单，确保每个功能都能测试
2. **检测该prefab是否为特效** → 验证GfxRoot_Unity组件
3. **遍历ParticleSystem，筛选可转换的组件** → 找到符合条件的粒子系统
4. **转换基础属性** → mesh filter + mesh renderer + GfxAnimation_Unity时间配置
5. **转换Color over Lifetime** → GfxMaterialAnimation_Unity组件
6. **转换Rotation over Lifetime** → GfxAnimation_Unity旋转模块
7. **转换Size over Lifetime** → GfxAnimation_Unity缩放模块
8. **删除ParticleSystem并保存prefab** → 完成转换

## 📊 任务优先级

### 🟢 P0 - 核心流程 (按顺序完成)
- 每个转换步骤的核心实现
- 基础数据结构支持

### 🟡 P1 - 支撑功能 (配合核心)
- 错误处理和日志
- 用户界面

### 🔵 P2 - 增强功能 (最后完成)
- 单元测试
- 性能优化

---

## 📝 按流程排序的任务清单

### 步骤1：创建执行入口和基础框架 (P0)

#### 任务1.1：创建右键菜单入口
- [x] 创建 `ParticleToGfxContextMenu.cs` - Unity编辑器右键菜单
- [x] 实现 `[MenuItem("TATools/粒子转Gfx", false, 10)]` 菜单项
- [x] 实现菜单项验证 `[MenuItem("TATools/粒子转Gfx", true)]`
- [x] 创建 `ConversionController.cs` - 转换控制器主类
- [x] 实现 `ConvertPrefab(GameObject prefab)` 主方法（先返回简单日志）

**测试标准**：
- 在Hierarchy中选择GameObject后，右键菜单中出现"粒子转Gfx"选项
- 点击菜单项后在Console中显示"开始转换"日志
- 选择非GameObject时菜单项置灰

**注意事项**：
- 使用Unity编辑器扩展API
- 验证 `Selection.activeGameObject` 不为null
- 先创建简单的框架，后续步骤逐步完善功能

#### 任务1.2：创建基础工具类
- [x] 创建 `ConversionLogger.cs` - 日志记录器
- [x] 实现 `LogInfo()`, `LogWarning()`, `LogError()` 方法
- [x] 创建 `GfxFrameworkHelper.cs` - GfxFramework辅助类
- [x] 实现 `HasGfxRoot(GameObject prefab)` 方法

**测试标准**：
- 通过右键菜单可以触发GfxRoot检测
- 能够正确检测包含GfxRoot_Unity组件的Prefab
- 能够正确拒绝不包含GfxRoot_Unity组件的Prefab
- 在Console中显示检测结果

**注意事项**：
- 使用 `GetComponent<GfxRoot_Unity>()` 检测组件
- 提供清晰的日志输出格式
- 处理null值和异常情况

---

### 步骤2：检测Prefab是否为特效 (P0)

#### 任务2.1：完善特效检测逻辑
- [x] 在 `ConversionController.ConvertPrefab()` 中添加GfxRoot检测
- [x] 如果不是特效Prefab，显示警告并停止转换
- [x] 如果是特效Prefab，显示成功信息并继续

**测试标准**：
- 选择包含GfxRoot_Unity的Prefab，转换继续进行
- 选择不包含GfxRoot_Unity的GameObject，显示警告并停止
- Console中显示清晰的检测结果

**注意事项**：
- 在转换流程的最开始进行检测
- 提供用户友好的错误信息
- 为后续步骤做好准备

---

### 步骤3：遍历ParticleSystem，筛选可转换的组件 (P0)

#### 任务3.1：创建配置化验证系统
- [x] 创建 `ValidationConfig.cs` - 验证配置数据结构（ScriptableObject）
- [x] 创建 `ExpressionRule.cs` - 表达式规则数据结构
- [x] 创建默认验证配置文件 `DefaultConversionRules.asset`
- [x] 配置PRD中的基础规则：
  - [x] `main.maxParticles == 1`
  - [x] `renderer.renderMode == 4 && renderer.mesh != null` (4=Mesh模式)
  - [x] 23个禁用模块的检查规则

**测试标准**：
- 在Project窗口中可以看到配置文件
- 在Inspector中可以编辑验证规则
- 配置文件包含所有PRD要求的筛选条件

#### 任务3.2：创建参数发现工具
- [ ] 创建 `ParameterExplorer.cs` - 参数探测器窗口
- [ ] 实现 `[MenuItem("TATools/粒子系统参数探测器")]` 菜单
- [ ] 实现实时参数值显示功能
- [ ] 实现参数名复制功能
- [ ] 创建 `ParameterDiscovery.cs` - 自动文档生成工具
- [ ] 实现 `[MenuItem("TATools/生成粒子系统参数文档")]` 菜单

**测试标准**：
- 可以通过菜单打开参数探测器窗口
- 选择ParticleSystem后可以看到所有参数和值
- 可以复制参数名用于配置
- 可以生成完整的参数文档文件

#### 任务3.3：创建配置驱动的验证器
- [ ] 创建 `IParticleSystemValidator.cs` - 验证器接口
- [ ] 创建 `ConfigurableValidator.cs` - 配置驱动的验证器
- [ ] 实现表达式解析和执行引擎
- [ ] 实现 `ValidationResult` 数据结构
- [ ] 创建 `ConfigValidator.cs` - 配置验证工具

**测试标准**：
- 可以根据配置文件验证ParticleSystem
- 表达式解析正确，支持基础运算符
- 验证失败时提供详细错误信息
- 配置错误时有友好提示

#### 任务3.4：创建粒子系统分析器
- [ ] 创建 `ParticleSystemAnalyzer.cs` - 粒子系统分析器
- [ ] 实现 `FindValidParticleSystems(GameObject prefab, ValidationConfig config)` 方法
- [ ] 集成配置化验证器
- [ ] 实现详细的验证结果报告
- [ ] 在 `ConversionController.ConvertPrefab()` 中调用分析器

**测试标准**：
- 通过右键菜单可以触发粒子系统分析
- 能够找到Prefab中所有的ParticleSystem组件
- 能够根据配置文件正确筛选ParticleSystem
- 在Console中显示详细的验证结果和统计信息
- 可以通过修改配置文件改变筛选行为

**注意事项**：
- 使用 `GetComponentsInChildren<ParticleSystem>()` 遍历
- 验证逻辑完全由配置文件驱动
- 提供详细的验证失败原因
- 支持配置文件的热重载
- 如果没有符合条件的粒子系统，显示提示并停止

---

### 步骤4：转换基础属性到Mesh组件和GfxAnimation时间 (P0)

#### 任务4.1：创建基础转换功能
- [ ] 创建 `MeshComponentCreator.cs` - Mesh组件创建器
- [ ] 实现 `CreateMeshFilter(GameObject target, Mesh mesh)` 方法
- [ ] 实现 `CreateMeshRenderer(GameObject target, Material material)` 方法
- [ ] 创建 `GfxAnimationCreator.cs` - GfxAnimation创建器
- [ ] 实现 `CreateGfxAnimationWithTime(GameObject target, float startTime, float endTime)` 方法
- [ ] 在 `ConversionController.ConvertPrefab()` 中调用基础转换

**测试标准**：
- 通过右键菜单可以触发基础转换
- 能够为GameObject添加MeshFilter和MeshRenderer组件
- 能够正确设置Mesh和Material
- 能够添加GfxAnimation_Unity组件并设置开始/结束时间
- 在Inspector中可以看到新添加的组件

**注意事项**：
- 使用 `AddComponent<>()` 添加组件
- 从ParticleSystemRenderer获取Mesh和Material
- 从ParticleSystem.main获取StartDelay和StartLifetime
- 设置GfxAnimation的m_timeSystem属性
- 确保组件添加成功后再继续

---

### 步骤5：转换Color over Lifetime到GfxMaterialAnimation (P0)

#### 任务5.1：实现颜色动画转换
- [ ] 创建 `ColorAnimationConverter.cs` - 颜色动画转换器
- [ ] 实现 `HasColorOverLifetime(ParticleSystem ps)` 检查方法
- [ ] 实现 `CreateGfxMaterialAnimation(GameObject target, Gradient colorGradient)` 方法
- [ ] 配置GfxMaterialColorCurve的属性名为"_Color"
- [ ] 在 `ConversionController.ConvertPrefab()` 中调用颜色转换

**测试标准**：
- 通过右键菜单可以触发颜色动画转换
- 能够检测ParticleSystem是否启用了Color over Lifetime
- 能够添加GfxMaterialAnimation_Unity组件
- 能够正确设置颜色渐变曲线
- 在Inspector中可以看到颜色动画配置

**注意事项**：
- 检查 `colorOverLifetime.enabled` 状态
- 获取 `colorOverLifetime.color.gradient`
- 创建GfxMaterialColorCurve并添加到组件
- 设置正确的材质属性名
- 只有启用了Color over Lifetime才添加组件

---

### 步骤6：转换Rotation over Lifetime到GfxAnimation旋转模块 (P0)

#### 任务6.1：实现旋转动画转换
- [ ] 创建 `RotationAnimationConverter.cs` - 旋转动画转换器
- [ ] 实现 `HasRotationOverLifetime(ParticleSystem ps)` 检查方法
- [ ] 实现 `ConfigureRotationModule(GfxAnimation_Unity gfxAnim, ParticleSystem ps)` 方法
- [ ] 处理ParticleSystem.MinMaxCurve到AnimationCurve的转换
- [ ] 在 `ConversionController.ConvertPrefab()` 中调用旋转转换

**测试标准**：
- 通过右键菜单可以触发旋转动画转换
- 能够检测ParticleSystem是否启用了Rotation over Lifetime
- 能够配置GfxAnimation的旋转模块
- 能够正确转换旋转曲线数据
- 在Inspector中可以看到旋转动画配置

**注意事项**：
- 检查 `rotationOverLifetime.enabled` 状态
- 处理 `separateAxes` 的情况
- 转换MinMaxCurve到AnimationCurve
- 启用GfxRotModule并设置曲线
- 确保已有GfxAnimation组件才配置模块

---

### 步骤7：转换Size over Lifetime到GfxAnimation缩放模块 (P0)

#### 任务7.1：实现缩放动画转换
- [ ] 创建 `ScaleAnimationConverter.cs` - 缩放动画转换器
- [ ] 实现 `HasSizeOverLifetime(ParticleSystem ps)` 检查方法
- [ ] 实现 `ConfigureScaleModule(GfxAnimation_Unity gfxAnim, ParticleSystem ps)` 方法
- [ ] 处理统一缩放和分轴缩放的情况
- [ ] 在 `ConversionController.ConvertPrefab()` 中调用缩放转换

**测试标准**：
- 通过右键菜单可以触发缩放动画转换
- 能够检测ParticleSystem是否启用了Size over Lifetime
- 能够配置GfxAnimation的缩放模块
- 能够正确转换缩放曲线数据
- 在Inspector中可以看到缩放动画配置

**注意事项**：
- 检查 `sizeOverLifetime.enabled` 状态
- 处理 `separateAxes` 的情况
- 转换MinMaxCurve到AnimationCurve
- 启用GfxScaleModule并设置曲线
- 确保已有GfxAnimation组件才配置模块

---

### 步骤8：删除ParticleSystem并保存Prefab (P0)

#### 任务8.1：完成转换流程
- [ ] 创建 `ConversionFinalizer.cs` - 转换完成器
- [ ] 实现 `DisableParticleSystem(ParticleSystem ps)` 方法
- [ ] 实现 `SaveConvertedPrefab(GameObject prefab, string originalPath)` 方法
- [ ] 生成带_Gfx后缀的新Prefab文件
- [ ] 在 `ConversionController.ConvertPrefab()` 中调用完成器

**测试标准**：
- 通过右键菜单可以完成整个转换流程
- 能够禁用或删除原始ParticleSystem组件
- 能够保存新的Prefab文件到正确路径
- 新Prefab包含所有转换后的组件
- 在Project窗口中可以看到新生成的Prefab

**注意事项**：
- 使用 `SetActive(false)` 禁用ParticleSystem
- 使用 `PrefabUtility.SaveAsPrefabAsset()` 保存
- 生成正确的文件路径和命名
- 保持原始Prefab不变
- 在转换完成后显示成功信息

---

### 步骤9：测试和优化 (P2)

#### 任务9.1：创建测试用例
- [ ] 创建测试用的Prefab文件
- [ ] 编写单元测试
- [ ] 进行集成测试
- [ ] 性能测试和优化

**测试标准**：
- 所有功能都有对应的测试用例
- 测试覆盖率达到要求
- 性能满足使用需求
- 代码质量符合规范

---

## 🎯 开发里程碑

### 里程碑1：执行入口完成 (步骤1)
- 右键菜单功能正常，可以触发转换流程
- 基础框架和日志功能可用
- **测试方法**：右键菜单出现，点击后有日志输出

### 里程碑2：特效检测完成 (步骤2)
- 能够正确检测Prefab是否为特效
- GfxRoot检测逻辑准确
- **测试方法**：选择不同类型的Prefab，验证检测结果

### 里程碑3：配置化验证系统完成 (步骤3)
- 配置化验证系统可用，支持表达式规则
- 参数发现工具可以帮助编写新规则
- 能够找到并筛选符合条件的ParticleSystem
- 验证逻辑完全由配置文件驱动
- **测试方法**：
  - 使用参数探测器查看ParticleSystem参数
  - 修改配置文件中的规则，验证筛选行为改变
  - 使用包含多个ParticleSystem的Prefab，验证筛选结果

### 里程碑4：基础转换完成 (步骤4)
- 能够创建Mesh组件和GfxAnimation时间配置
- 基础转换流程可用
- **测试方法**：转换简单的粒子系统，检查生成的组件

### 里程碑5：颜色动画转换完成 (步骤5)
- Color over Lifetime转换功能正常
- GfxMaterialAnimation组件配置正确
- **测试方法**：转换带颜色动画的粒子系统

### 里程碑6：旋转动画转换完成 (步骤6)
- Rotation over Lifetime转换功能正常
- GfxAnimation旋转模块配置正确
- **测试方法**：转换带旋转动画的粒子系统

### 里程碑7：缩放动画转换完成 (步骤7)
- Size over Lifetime转换功能正常
- GfxAnimation缩放模块配置正确
- **测试方法**：转换带缩放动画的粒子系统

### 里程碑8：完整转换流程完成 (步骤8)
- 能够删除原始ParticleSystem并保存新Prefab
- 完整的转换流程可用
- **测试方法**：端到端转换测试，验证最终结果

## 📋 每步骤的测试方法

### 🧪 测试策略
1. **单步测试**：每完成一个步骤，立即测试该步骤的功能
2. **增量测试**：在前一步骤基础上测试新功能
3. **回归测试**：确保新功能不影响已有功能
4. **端到端测试**：完整流程的综合测试

### 🔧 测试工具
- **Unity Console**：查看日志输出和错误信息
- **Inspector窗口**：检查组件配置和属性设置
- **Project窗口**：验证生成的Prefab文件
- **Scene窗口**：观察转换后的效果

### 📝 测试数据准备
建议准备以下测试用的Prefab：
1. **基础测试Prefab**：包含GfxRoot_Unity和简单ParticleSystem（MaxParticles=1，RenderMode=Mesh）
2. **颜色动画Prefab**：启用Color over Lifetime的粒子系统
3. **旋转动画Prefab**：启用Rotation over Lifetime的粒子系统
4. **缩放动画Prefab**：启用Size over Lifetime的粒子系统
5. **复合动画Prefab**：同时启用多种动画的粒子系统
6. **无效Prefab**：不符合转换条件的测试用例
7. **配置测试Prefab**：用于测试不同配置规则的各种ParticleSystem组合
8. **边界条件Prefab**：测试边界值和特殊情况的ParticleSystem

## 📋 注意事项

1. **编译验证**：每完成一个任务都要触发Unity编译，确保无错误
2. **步骤测试**：每完成一个步骤都要进行功能测试，确保该步骤正常工作
3. **日志记录**：在每个关键步骤添加日志输出，便于调试和验证
4. **错误处理**：从第一个步骤开始就要考虑错误处理
5. **代码规范**：遵循项目代码规范和命名约定

## 🚀 开始开发

建议从**步骤1：任务1.1**开始，按照步骤顺序逐步实现。每个任务完成后：
1. 在清单中标记为 `[x]`
2. 触发Unity编译验证
3. 进行该步骤的功能测试
4. 记录测试结果和发现的问题
