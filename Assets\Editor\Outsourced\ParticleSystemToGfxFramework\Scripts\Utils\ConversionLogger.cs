using UnityEngine;

namespace ParticleSystemToGfxFramework.Utils
{
    /// <summary>
    /// 转换过程的日志记录器，提供统一的日志输出格式
    /// </summary>
    public static class ConversionLogger
    {
        private const string LOG_PREFIX = "[粒子转Gfx]";
        
        /// <summary>
        /// 记录信息级别的日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void LogInfo(string message)
        {
            Debug.Log($"{LOG_PREFIX} {message}");
        }
        
        /// <summary>
        /// 记录警告级别的日志
        /// </summary>
        /// <param name="message">警告消息</param>
        public static void LogWarning(string message)
        {
            Debug.LogWarning($"{LOG_PREFIX} {message}");
        }
        
        /// <summary>
        /// 记录错误级别的日志
        /// </summary>
        /// <param name="message">错误消息</param>
        public static void LogError(string message)
        {
            Debug.LogError($"{LOG_PREFIX} {message}");
        }
        
        /// <summary>
        /// 记录带异常信息的错误日志
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="exception">异常对象</param>
        public static void LogError(string message, System.Exception exception)
        {
            Debug.LogError($"{LOG_PREFIX} {message}\n异常详情: {exception.Message}");
        }
        
        /// <summary>
        /// 记录转换步骤开始的日志
        /// </summary>
        /// <param name="stepName">步骤名称</param>
        /// <param name="targetName">目标对象名称</param>
        public static void LogStepStart(string stepName, string targetName)
        {
            LogInfo($"开始{stepName}: {targetName}");
        }
        
        /// <summary>
        /// 记录转换步骤完成的日志
        /// </summary>
        /// <param name="stepName">步骤名称</param>
        /// <param name="result">步骤结果</param>
        public static void LogStepComplete(string stepName, string result)
        {
            LogInfo($"{stepName}完成: {result}");
        }
        
        /// <summary>
        /// 记录转换统计信息
        /// </summary>
        /// <param name="totalCount">总数量</param>
        /// <param name="convertedCount">转换成功数量</param>
        /// <param name="skippedCount">跳过数量</param>
        public static void LogStatistics(int totalCount, int convertedCount, int skippedCount)
        {
            LogInfo($"转换统计 - 总计: {totalCount}, 成功: {convertedCount}, 跳过: {skippedCount}");
        }
    }
}
