using UnityEngine;
using ParticleSystemToGfxFramework.Data;

namespace ParticleSystemToGfxFramework.Core
{
    /// <summary>
    /// 配置驱动的粒子系统验证器
    /// 根据ValidationConfig中的表达式规则验证ParticleSystem
    /// </summary>
    public class ConfigurableValidator : IParticleSystemValidator
    {
        private readonly ExpressionEngine _expressionEngine;
        
        public ConfigurableValidator()
        {
            _expressionEngine = new ExpressionEngine();
        }
        
        /// <summary>
        /// 验证粒子系统是否符合配置要求
        /// </summary>
        /// <param name="particleSystem">要验证的粒子系统</param>
        /// <param name="config">验证配置</param>
        /// <returns>详细的验证结果</returns>
        public ValidationResult Validate(ParticleSystem particleSystem, ValidationConfig config)
        {
            var result = new ValidationResult();
            result.TargetName = particleSystem.name;
            
            if (config == null || config.Rules == null)
            {
                result.AddError("配置错误", "验证配置为空或规则列表为空");
                result.GenerateSummary();
                return result;
            }
            
            // 遍历所有验证规则
            foreach (var rule in config.Rules)
            {
                if (rule == null || string.IsNullOrEmpty(rule.expression))
                {
                    result.AddWarning("规则跳过", $"规则 '{rule?.name}' 的表达式为空，已跳过");
                    continue;
                }
                
                try
                {
                    // 执行表达式
                    bool expressionResult = _expressionEngine.EvaluateExpression(rule.expression, particleSystem);
                    
                    // 根据表达式结果和规则严重性处理
                    if (!expressionResult)
                    {
                        string message = !string.IsNullOrEmpty(rule.errorMessage) 
                            ? rule.errorMessage 
                            : $"规则 '{rule.name}' 验证失败";
                            
                        if (rule.severity == ValidationSeverity.Error)
                        {
                            result.AddError(rule.name, message, rule.expression);
                        }
                        else if (rule.severity == ValidationSeverity.Warning)
                        {
                            result.AddWarning(rule.name, message, rule.expression);
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    result.AddError("表达式执行错误", 
                        $"规则 '{rule.name}' 执行失败: {ex.Message}", 
                        rule.expression);
                }
            }
            
            result.GenerateSummary();
            return result;
        }
        
        /// <summary>
        /// 快速检查粒子系统是否有效
        /// </summary>
        /// <param name="particleSystem">要验证的粒子系统</param>
        /// <param name="config">验证配置</param>
        /// <returns>是否通过验证</returns>
        public bool IsValid(ParticleSystem particleSystem, ValidationConfig config)
        {
            var result = Validate(particleSystem, config);
            return result.IsValid;
        }
        
        /// <summary>
        /// 验证单个表达式规则
        /// </summary>
        /// <param name="particleSystem">粒子系统</param>
        /// <param name="rule">表达式规则</param>
        /// <returns>是否通过验证</returns>
        public bool ValidateRule(ParticleSystem particleSystem, ExpressionRule rule)
        {
            if (rule == null || string.IsNullOrEmpty(rule.expression))
                return true; // 空规则视为通过
                
            try
            {
                return _expressionEngine.EvaluateExpression(rule.expression, particleSystem);
            }
            catch (System.Exception ex)
            {
                ConversionLogger.LogError($"规则 '{rule.name}' 执行失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 获取表达式执行引擎（用于测试）
        /// </summary>
        public ExpressionEngine GetExpressionEngine()
        {
            return _expressionEngine;
        }
    }
}
