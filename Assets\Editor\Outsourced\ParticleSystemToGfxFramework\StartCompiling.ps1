Add-Type -TypeDefinition @'
using System;
using System.Runtime.InteropServices;

public class Win32 {
    [DllImport("user32.dll", CharSet = CharSet.Auto)]
    public static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

    [DllImport("user32.dll", CharSet = CharSet.Auto)]
    public static extern int GetWindowText(IntPtr hWnd, System.Text.StringBuilder lpString, int nMaxCount);

    [DllImport("user32.dll")]
    public static extern bool SetForegroundWindow(IntPtr hWnd);

    [DllImport("user32.dll")]
    public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

    [DllImport("user32.dll")]
    public static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);
    public delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

    public const int SW_MINIMIZE = 6;   // 最小化窗口
    public const int SW_RESTORE = 9;     // 恢复窗口
}
'@

# 定义要匹配的窗口标题关键词（如 "Unity"）
$keyword = "Unity 2018.4.25f1"

# 枚举所有窗口，强制激活包含关键词的窗口
$windowScriptBlock = {
    $hwnd = $args[0]
    $sb = New-Object System.Text.StringBuilder 256
    $null = [Win32]::GetWindowText($hwnd, $sb, $sb.Capacity)
    $title = $sb.ToString()

    if ($title -like "*$keyword*") {
        # 先最小化窗口（强制改变状态）
        [Win32]::ShowWindow($hwnd, [Win32]::SW_MINIMIZE)
        # 恢复窗口并激活焦点
        [Win32]::ShowWindow($hwnd, [Win32]::SW_RESTORE)
        [Win32]::SetForegroundWindow($hwnd)
    }
    return $true  # 继续枚举其他窗口（若需仅激活第一个，改为 return $false）
}

# 执行窗口枚举
$delegate = [Win32+EnumWindowsProc]$windowScriptBlock
[Win32]::EnumWindows($delegate, [IntPtr]::Zero)