using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using ParticleSystemToGfxFramework.Data;
using ParticleSystemToGfxFramework.Utils;

namespace ParticleSystemToGfxFramework.Core
{
    /// <summary>
    /// 粒子系统分析器
    /// 分析Prefab中的所有ParticleSystem组件，筛选出符合转换条件的组件
    /// </summary>
    public class ParticleSystemAnalyzer
    {
        private readonly IParticleSystemValidator _validator;
        
        public ParticleSystemAnalyzer()
        {
            _validator = new ConfigurableValidator();
        }
        
        public ParticleSystemAnalyzer(IParticleSystemValidator validator)
        {
            _validator = validator ?? new ConfigurableValidator();
        }
        
        /// <summary>
        /// 查找Prefab中符合条件的粒子系统
        /// </summary>
        /// <param name="prefab">要分析的Prefab</param>
        /// <param name="config">验证配置</param>
        /// <returns>分析结果</returns>
        public AnalysisResult FindValidParticleSystems(GameObject prefab, ValidationConfig config)
        {
            var result = new AnalysisResult();
            result.TargetPrefab = prefab;
            
            if (prefab == null)
            {
                result.ErrorMessage = "Prefab为空";
                return result;
            }
            
            if (config == null)
            {
                result.ErrorMessage = "验证配置为空";
                return result;
            }
            
            try
            {
                // 获取Prefab中的所有ParticleSystem组件
                ParticleSystem[] allParticleSystems = prefab.GetComponentsInChildren<ParticleSystem>();
                result.TotalParticleSystemCount = allParticleSystems.Length;
                
                ConversionLogger.LogInfo($"在Prefab '{prefab.name}' 中找到 {allParticleSystems.Length} 个ParticleSystem组件");
                
                if (allParticleSystems.Length == 0)
                {
                    result.ErrorMessage = "Prefab中没有找到ParticleSystem组件";
                    return result;
                }
                
                // 逐个验证ParticleSystem
                foreach (var particleSystem in allParticleSystems)
                {
                    if (particleSystem == null) continue;
                    
                    ValidationResult validationResult = _validator.Validate(particleSystem, config);
                    
                    if (validationResult.IsValid)
                    {
                        result.ValidParticleSystems.Add(particleSystem);
                        ConversionLogger.LogInfo($"ParticleSystem '{particleSystem.name}' 验证通过");
                    }
                    else
                    {
                        result.RejectedParticleSystems.Add(particleSystem, validationResult);
                        ConversionLogger.LogWarning($"ParticleSystem '{particleSystem.name}' 验证失败: {validationResult.Summary}");
                        
                        // 记录详细错误信息
                        foreach (var error in validationResult.Errors)
                        {
                            ConversionLogger.LogWarning($"  - [{error.RuleName}] {error.Message}");
                        }
                    }
                }
                
                // 生成统计信息
                result.GenerateStatistics();
                
                // 记录分析结果
                ConversionLogger.LogInfo($"分析完成: 总计 {result.TotalParticleSystemCount} 个，" +
                    $"符合条件 {result.ValidParticleSystemCount} 个，" +
                    $"不符合条件 {result.RejectedParticleSystemCount} 个");
                
                return result;
            }
            catch (System.Exception ex)
            {
                result.ErrorMessage = $"分析过程中发生错误: {ex.Message}";
                ConversionLogger.LogError(result.ErrorMessage);
                return result;
            }
        }
        
        /// <summary>
        /// 快速检查Prefab是否包含有效的粒子系统
        /// </summary>
        /// <param name="prefab">要检查的Prefab</param>
        /// <param name="config">验证配置</param>
        /// <returns>是否包含有效的粒子系统</returns>
        public bool HasValidParticleSystems(GameObject prefab, ValidationConfig config)
        {
            var result = FindValidParticleSystems(prefab, config);
            return result.ValidParticleSystemCount > 0;
        }
        
        /// <summary>
        /// 获取Prefab中所有ParticleSystem的详细信息
        /// </summary>
        /// <param name="prefab">要分析的Prefab</param>
        /// <returns>ParticleSystem信息列表</returns>
        public List<ParticleSystemInfo> GetParticleSystemInfos(GameObject prefab)
        {
            var infos = new List<ParticleSystemInfo>();
            
            if (prefab == null) return infos;
            
            ParticleSystem[] allParticleSystems = prefab.GetComponentsInChildren<ParticleSystem>();
            
            foreach (var ps in allParticleSystems)
            {
                if (ps != null)
                {
                    infos.Add(new ParticleSystemInfo
                    {
                        ParticleSystem = ps,
                        Name = ps.name,
                        GameObject = ps.gameObject,
                        MaxParticles = ps.main.maxParticles,
                        IsPlaying = ps.isPlaying,
                        HasRenderer = ps.GetComponent<ParticleSystemRenderer>() != null
                    });
                }
            }
            
            return infos;
        }
    }
    
    /// <summary>
    /// 分析结果
    /// </summary>
    public class AnalysisResult
    {
        /// <summary>
        /// 目标Prefab
        /// </summary>
        public GameObject TargetPrefab { get; set; }
        
        /// <summary>
        /// 符合条件的粒子系统列表
        /// </summary>
        public List<ParticleSystem> ValidParticleSystems { get; set; }
        
        /// <summary>
        /// 不符合条件的粒子系统及其验证结果
        /// </summary>
        public Dictionary<ParticleSystem, ValidationResult> RejectedParticleSystems { get; set; }
        
        /// <summary>
        /// 总粒子系统数量
        /// </summary>
        public int TotalParticleSystemCount { get; set; }
        
        /// <summary>
        /// 符合条件的粒子系统数量
        /// </summary>
        public int ValidParticleSystemCount => ValidParticleSystems.Count;
        
        /// <summary>
        /// 不符合条件的粒子系统数量
        /// </summary>
        public int RejectedParticleSystemCount => RejectedParticleSystems.Count;
        
        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 是否分析成功
        /// </summary>
        public bool IsSuccess => string.IsNullOrEmpty(ErrorMessage);
        
        /// <summary>
        /// 是否有符合条件的粒子系统
        /// </summary>
        public bool HasValidParticleSystems => ValidParticleSystemCount > 0;
        
        public AnalysisResult()
        {
            ValidParticleSystems = new List<ParticleSystem>();
            RejectedParticleSystems = new Dictionary<ParticleSystem, ValidationResult>();
            TotalParticleSystemCount = 0;
            ErrorMessage = "";
        }
        
        /// <summary>
        /// 生成统计信息
        /// </summary>
        public void GenerateStatistics()
        {
            // 统计信息已通过属性自动计算
        }
        
        /// <summary>
        /// 获取详细报告
        /// </summary>
        public string GetDetailedReport()
        {
            var report = new System.Text.StringBuilder();
            
            report.AppendLine($"=== 粒子系统分析报告 ===");
            report.AppendLine($"目标Prefab: {TargetPrefab?.name ?? "未知"}");
            report.AppendLine($"分析状态: {(IsSuccess ? "成功" : "失败")}");
            
            if (!IsSuccess)
            {
                report.AppendLine($"错误信息: {ErrorMessage}");
                return report.ToString();
            }
            
            report.AppendLine($"总计粒子系统: {TotalParticleSystemCount} 个");
            report.AppendLine($"符合条件: {ValidParticleSystemCount} 个");
            report.AppendLine($"不符合条件: {RejectedParticleSystemCount} 个");
            
            if (ValidParticleSystemCount > 0)
            {
                report.AppendLine("\n符合条件的粒子系统:");
                foreach (var ps in ValidParticleSystems)
                {
                    report.AppendLine($"  ✓ {ps.name}");
                }
            }
            
            if (RejectedParticleSystemCount > 0)
            {
                report.AppendLine("\n不符合条件的粒子系统:");
                foreach (var kvp in RejectedParticleSystems)
                {
                    report.AppendLine($"  ✗ {kvp.Key.name}: {kvp.Value.Summary}");
                }
            }
            
            return report.ToString();
        }
    }
    
    /// <summary>
    /// 粒子系统信息
    /// </summary>
    public class ParticleSystemInfo
    {
        public ParticleSystem ParticleSystem { get; set; }
        public string Name { get; set; }
        public GameObject GameObject { get; set; }
        public int MaxParticles { get; set; }
        public bool IsPlaying { get; set; }
        public bool HasRenderer { get; set; }
    }
}
