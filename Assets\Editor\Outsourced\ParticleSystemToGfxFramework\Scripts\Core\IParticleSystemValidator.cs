using UnityEngine;

namespace ParticleSystemToGfxFramework.Core
{
    /// <summary>
    /// 粒子系统验证器接口
    /// 定义验证ParticleSystem是否符合转换条件的标准接口
    /// </summary>
    public interface IParticleSystemValidator
    {
        /// <summary>
        /// 验证粒子系统是否符合配置要求
        /// </summary>
        /// <param name="particleSystem">要验证的粒子系统</param>
        /// <param name="config">验证配置</param>
        /// <returns>详细的验证结果</returns>
        ValidationResult Validate(ParticleSystem particleSystem, ValidationConfig config);
        
        /// <summary>
        /// 快速检查粒子系统是否有效
        /// </summary>
        /// <param name="particleSystem">要验证的粒子系统</param>
        /// <param name="config">验证配置</param>
        /// <returns>是否通过验证</returns>
        bool IsValid(ParticleSystem particleSystem, ValidationConfig config);
    }
}
