# Path Configuration
$projectPath = "D:\svn-JK_TFT_other"
$scriptPS1   = "$projectPath\StartCompiling.ps1"
$editorLog   = "$env:USERPROFILE\AppData\Local\Unity\Editor\Editor.log"

# Check if Editor.log exists
if (-not (Test-Path $editorLog)) {
    Write-Host "Error: Editor.log file not found: $editorLog" -ForegroundColor Red
    Write-Host "Please make sure Unity is running, or update the editorLog path in the script."
    exit 1
}

# Switch to Unity window
Write-Host "[$(Get-Date -Format G)] Switching to Unity window..." -ForegroundColor Cyan
& $scriptPS1

# Wait for Unity to compile (5 seconds)
Write-Host "Waiting for Unity to compile (5 seconds)..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Read the latest 100 lines of the compilation log, filter for warnings and errors
Write-Host "-- Compilation Results --" -ForegroundColor Cyan
Write-Host "Reading warnings and errors from Editor.log..." -ForegroundColor Gray

# Read the latest 100 lines and filter for lines containing ": warning " and ": error "
$logContent = Get-Content $editorLog -Tail 100
$warnings = $logContent | Where-Object { $_ -match ': warning ' }
$errors = $logContent | Where-Object { $_ -match ': error ' }

# Display errors and warnings
foreach ($line in $errors) {
    Write-Host $line -ForegroundColor Red
}

foreach ($line in $warnings) {
    Write-Host $line -ForegroundColor Yellow
}

# Check if there are any warnings or errors
if (($errors.Count -eq 0) -and ($warnings.Count -eq 0)) {
    Write-Host "No warnings or errors found" -ForegroundColor Green
} else {
    Write-Host "Found $($errors.Count) errors, $($warnings.Count) warnings" -ForegroundColor Cyan
}
