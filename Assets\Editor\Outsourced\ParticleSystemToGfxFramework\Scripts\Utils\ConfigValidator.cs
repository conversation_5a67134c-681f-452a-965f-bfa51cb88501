using UnityEngine;
using ParticleSystemToGfxFramework.Data;

namespace ParticleSystemToGfxFramework.Utils
{
    /// <summary>
    /// 配置验证工具
    /// 验证ValidationConfig配置文件的有效性
    /// </summary>
    public static class ConfigValidator
    {
        /// <summary>
        /// 验证配置文件是否有效
        /// </summary>
        /// <param name="config">要验证的配置</param>
        /// <returns>验证结果</returns>
        public static ConfigValidationResult ValidateConfig(ValidationConfig config)
        {
            var result = new ConfigValidationResult();
            
            if (config == null)
            {
                result.AddError("配置为空", "ValidationConfig对象为null");
                return result;
            }
            
            if (config.Rules == null)
            {
                result.AddError("规则列表为空", "Rules数组为null");
                return result;
            }

            if (config.Rules.Length == 0)
            {
                result.AddWarning("规则列表为空", "Rules数组长度为0，没有验证规则");
                return result;
            }

            // 验证每个规则
            for (int i = 0; i < config.Rules.Length; i++)
            {
                var rule = config.Rules[i];
                ValidateRule(rule, i, result);
            }
            
            result.GenerateSummary();
            return result;
        }
        
        /// <summary>
        /// 验证单个规则
        /// </summary>
        /// <param name="rule">要验证的规则</param>
        /// <param name="index">规则索引</param>
        /// <param name="result">验证结果</param>
        private static void ValidateRule(ExpressionRule rule, int index, ConfigValidationResult result)
        {
            string rulePrefix = $"规则[{index}]";
            
            if (rule == null)
            {
                result.AddError($"{rulePrefix} 为空", "ExpressionRule对象为null");
                return;
            }
            
            // 验证规则名称
            if (string.IsNullOrEmpty(rule.name))
            {
                result.AddWarning($"{rulePrefix} 名称为空", "建议为每个规则设置有意义的名称");
            }
            
            // 验证表达式
            if (string.IsNullOrEmpty(rule.expression))
            {
                result.AddError($"{rulePrefix} 表达式为空", "expression字段不能为空");
            }
            else
            {
                ValidateExpression(rule.expression, rulePrefix, result);
            }
            
            // 验证严重性 - ValidationSeverity是枚举类型，不需要验证
            
            // 验证错误信息
            if (string.IsNullOrEmpty(rule.errorMessage))
            {
                result.AddWarning($"{rulePrefix} 错误信息为空", "建议设置有意义的错误信息");
            }
        }
        
        /// <summary>
        /// 验证表达式语法
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <param name="rulePrefix">规则前缀</param>
        /// <param name="result">验证结果</param>
        private static void ValidateExpression(string expression, string rulePrefix, ConfigValidationResult result)
        {
            // 检查基本语法
            if (expression.Contains("==") || expression.Contains("!=") || 
                expression.Contains(">=") || expression.Contains("<=") || 
                expression.Contains(">") || expression.Contains("<") ||
                expression.Contains("&&") || expression.Contains("||") ||
                expression.StartsWith("!"))
            {
                // 表达式包含有效的运算符
            }
            else
            {
                result.AddWarning($"{rulePrefix} 表达式可能无效", 
                    $"表达式'{expression}'可能缺少比较运算符或逻辑运算符");
            }
            
            // 检查属性路径格式
            if (!expression.Contains(".") && !expression.StartsWith("!"))
            {
                result.AddWarning($"{rulePrefix} 属性路径格式", 
                    "表达式可能缺少属性路径，如main.maxParticles");
            }
            
            // 检查常见的属性名
            string[] validModules = { "main", "emission", "shape", "velocityoverlifetime", 
                "coloroverlifetime", "sizeoverlifetime", "rotationoverlifetime", "renderer" };
            
            bool hasValidModule = false;
            foreach (string module in validModules)
            {
                if (expression.ToLower().Contains(module))
                {
                    hasValidModule = true;
                    break;
                }
            }
            
            if (!hasValidModule && !expression.StartsWith("!"))
            {
                result.AddWarning($"{rulePrefix} 模块名称", 
                    "表达式可能包含无效的模块名称");
            }
        }
        
        /// <summary>
        /// 快速检查配置是否有效
        /// </summary>
        /// <param name="config">要检查的配置</param>
        /// <returns>是否有效</returns>
        public static bool IsConfigValid(ValidationConfig config)
        {
            var result = ValidateConfig(config);
            return result.IsValid;
        }
    }
    
    /// <summary>
    /// 配置验证结果
    /// </summary>
    public class ConfigValidationResult
    {
        public bool IsValid { get; private set; } = true;
        public System.Collections.Generic.List<string> Errors { get; private set; }
        public System.Collections.Generic.List<string> Warnings { get; private set; }
        public string Summary { get; private set; }
        
        public ConfigValidationResult()
        {
            Errors = new System.Collections.Generic.List<string>();
            Warnings = new System.Collections.Generic.List<string>();
        }
        
        public void AddError(string title, string message)
        {
            Errors.Add($"{title}: {message}");
            IsValid = false;
        }
        
        public void AddWarning(string title, string message)
        {
            Warnings.Add($"{title}: {message}");
        }
        
        public void GenerateSummary()
        {
            if (IsValid)
            {
                Summary = "配置验证通过";
                if (Warnings.Count > 0)
                {
                    Summary += $"，但有 {Warnings.Count} 个警告";
                }
            }
            else
            {
                Summary = $"配置验证失败：{Errors.Count} 个错误";
                if (Warnings.Count > 0)
                {
                    Summary += $"，{Warnings.Count} 个警告";
                }
            }
        }
        
        public string GetDetailedReport()
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine($"配置验证结果: {Summary}");
            
            if (Errors.Count > 0)
            {
                report.AppendLine("\n错误详情:");
                foreach (var error in Errors)
                {
                    report.AppendLine($"  ✗ {error}");
                }
            }
            
            if (Warnings.Count > 0)
            {
                report.AppendLine("\n警告详情:");
                foreach (var warning in Warnings)
                {
                    report.AppendLine($"  ⚠ {warning}");
                }
            }
            
            return report.ToString();
        }
    }
}
