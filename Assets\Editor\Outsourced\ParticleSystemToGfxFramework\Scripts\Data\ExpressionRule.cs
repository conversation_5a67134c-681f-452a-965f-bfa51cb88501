using UnityEngine;
using System;

namespace ParticleSystemToGfxFramework.Data
{
    /// <summary>
    /// 验证严重级别
    /// </summary>
    public enum ValidationSeverity
    {
        Info = 0,
        Warning = 1,
        Error = 2
    }
    

    
    /// <summary>
    /// 表达式验证规则
    /// </summary>
    [Serializable]
    public class ExpressionRule
    {
        [Header("规则信息")]
        public string name = "";
        public string description = "";
        
        [Header("表达式验证")]
        [TextArea(2, 4)]
        public string expression = "";
        
        [Header("结果处理")]
        public ValidationSeverity severity = ValidationSeverity.Error;
        public string errorMessage = "";
        
        /// <summary>
        /// 规则是否有效
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(name) && 
                   !string.IsNullOrEmpty(expression) && 
                   !string.IsNullOrEmpty(errorMessage);
        }
    }
}
