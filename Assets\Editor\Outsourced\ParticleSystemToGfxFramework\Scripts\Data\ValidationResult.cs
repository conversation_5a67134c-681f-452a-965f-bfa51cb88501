using System.Collections.Generic;
using System.Linq;

namespace ParticleSystemToGfxFramework.Data
{
    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// 是否通过验证
        /// </summary>
        public bool IsValid { get; set; }
        
        /// <summary>
        /// 验证错误列表
        /// </summary>
        public List<ValidationError> Errors { get; set; }
        
        /// <summary>
        /// 验证警告列表
        /// </summary>
        public List<ValidationWarning> Warnings { get; set; }
        
        /// <summary>
        /// 验证摘要信息
        /// </summary>
        public string Summary { get; set; }
        
        /// <summary>
        /// 被验证的对象名称
        /// </summary>
        public string TargetName { get; set; }
        
        public ValidationResult()
        {
            Errors = new List<ValidationError>();
            Warnings = new List<ValidationWarning>();
            IsValid = true;
            Summary = "";
            TargetName = "";
        }
        
        /// <summary>
        /// 添加错误
        /// </summary>
        public void AddError(string ruleName, string message, string expression = "")
        {
            Errors.Add(new ValidationError
            {
                RuleName = ruleName,
                Message = message,
                Expression = expression
            });
            IsValid = false;
        }
        
        /// <summary>
        /// 添加警告
        /// </summary>
        public void AddWarning(string ruleName, string message, string expression = "")
        {
            Warnings.Add(new ValidationWarning
            {
                RuleName = ruleName,
                Message = message,
                Expression = expression
            });
        }
        
        /// <summary>
        /// 生成摘要信息
        /// </summary>
        public void GenerateSummary()
        {
            if (IsValid)
            {
                Summary = $"验证通过";
                if (Warnings.Count > 0)
                {
                    Summary += $"，但有 {Warnings.Count} 个警告";
                }
            }
            else
            {
                Summary = $"验证失败：{Errors.Count} 个错误";
                if (Warnings.Count > 0)
                {
                    Summary += $"，{Warnings.Count} 个警告";
                }
            }
        }
        
        /// <summary>
        /// 获取详细报告
        /// </summary>
        public string GetDetailedReport()
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine($"验证目标: {TargetName}");
            report.AppendLine($"验证结果: {Summary}");
            
            if (Errors.Count > 0)
            {
                report.AppendLine("\n错误详情:");
                foreach (var error in Errors)
                {
                    report.AppendLine($"  - [{error.RuleName}] {error.Message}");
                    if (!string.IsNullOrEmpty(error.Expression))
                    {
                        report.AppendLine($"    表达式: {error.Expression}");
                    }
                }
            }
            
            if (Warnings.Count > 0)
            {
                report.AppendLine("\n警告详情:");
                foreach (var warning in Warnings)
                {
                    report.AppendLine($"  - [{warning.RuleName}] {warning.Message}");
                    if (!string.IsNullOrEmpty(warning.Expression))
                    {
                        report.AppendLine($"    表达式: {warning.Expression}");
                    }
                }
            }
            
            return report.ToString();
        }
    }
    
    /// <summary>
    /// 验证错误
    /// </summary>
    public class ValidationError
    {
        public string RuleName { get; set; }
        public string Message { get; set; }
        public string Expression { get; set; }
    }
    
    /// <summary>
    /// 验证警告
    /// </summary>
    public class ValidationWarning
    {
        public string RuleName { get; set; }
        public string Message { get; set; }
        public string Expression { get; set; }
    }
}
