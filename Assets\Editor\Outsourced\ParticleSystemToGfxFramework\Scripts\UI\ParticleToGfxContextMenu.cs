using UnityEngine;
using UnityEditor;

namespace ParticleSystemToGfxFramework.UI
{
    /// <summary>
    /// Unity编辑器右键菜单，提供粒子系统转Gfx框架的入口
    /// </summary>
    public static class ParticleToGfxContextMenu
    {
        /// <summary>
        /// 右键菜单项：粒子转Gfx
        /// </summary>
        [MenuItem("TATools/粒子转Gfx", false, 10)]
        public static void ConvertParticleToGfx()
        {
            var selectedObject = Selection.activeGameObject;
            
            if (selectedObject == null)
            {
                Debug.LogWarning("[粒子转Gfx] 请先选择一个GameObject");
                return;
            }
            
            Debug.Log($"[粒子转Gfx] 开始转换: {selectedObject.name}");
            
            // 调用转换控制器
            var controller = new Core.ConversionController();
            controller.ConvertPrefab(selectedObject);
        }
        
        /// <summary>
        /// 菜单项验证：只有选择了GameObject时才显示菜单项
        /// </summary>
        [MenuItem("TATools/粒子转Gfx", true)]
        public static bool ValidateConvertParticleToGfx()
        {
            return Selection.activeGameObject != null;
        }
    }
}
