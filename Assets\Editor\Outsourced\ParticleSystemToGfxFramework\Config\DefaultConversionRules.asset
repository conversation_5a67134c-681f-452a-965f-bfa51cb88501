%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f52912c59943cc84b8929ed65e66cb16, type: 3}
  m_Name: DefaultConversionRules
  m_EditorClassIdentifier: 
  version: 1.0
  description: "\u9ED8\u8BA4\u7C92\u5B50\u7CFB\u7EDF\u8F6C\u6362\u9A8C\u8BC1\u89C4\u5219"
  rules:
  - name: maxParticles
    description: "\u68C0\u67E5\u7C92\u5B50\u6700\u5927\u6570\u91CF\u5FC5\u987B\u4E3A1"
    expression: main.maxParticles == 1
    severity: 2
    errorMessage: "Max Particles\u5FC5\u987B\u7B49\u4E8E1"
  - name: renderModeAndMesh
    description: "\u68C0\u67E5\u6E32\u67D3\u6A21\u5F0F\u5FC5\u987B\u4E3AMesh\u4E14Mesh\u4E0D\u4E3A\u7A7A"
    expression: renderer.renderMode == 4 && renderer.mesh != null
    severity: 2
    errorMessage: "\u5FC5\u987B\u4F7F\u7528Mesh\u6E32\u67D3\u6A21\u5F0F\u4E14Mesh\u4E0D\u4E3A\u7A7A"
  - name: forbiddenModules
    description: "\u68C0\u67E5\u7981\u7528\u6A21\u5757\u672A\u542F\u7528"
    expression: '!shape.enabled && !velocityOverLifetime.enabled && !limitVelocityOverLifetime.enabled
      && !inheritVelocity.enabled && !forceOverLifetime.enabled && !colorBySpeed.enabled
      && !sizeBySpeed.enabled && !rotationBySpeed.enabled && !externalForces.enabled
      && !noise.enabled && !collision.enabled && !trigger.enabled && !subEmitters.enabled
      && !textureSheetAnimation.enabled && !lights.enabled && !trails.enabled'
    severity: 2
    errorMessage: "\u4E0D\u80FD\u542F\u7528\u4EE5\u4E0B\u7981\u7528\u6A21\u5757: Shape,
      Velocity over Lifetime, Limit Velocity over Lifetime, Inherit Velocity, Force
      over Lifetime, Color by Speed, Size by Speed, Rotation by Speed, External Forces,
      Noise, Collision, Triggers, Sub Emitters, Texture Sheet Animation, Lights, Trails"
