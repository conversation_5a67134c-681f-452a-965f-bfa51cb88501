using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;

namespace ParticleSystemToGfxFramework.Utils
{
    /// <summary>
    /// 粒子系统参数自动文档生成工具
    /// 生成完整的ParticleSystem属性参考文档
    /// </summary>
    public static class ParameterDiscovery
    {
        #region 菜单入口
        
        [MenuItem("TATools/生成粒子系统参数文档")]
        public static void GenerateParameterDocumentation()
        {
            try
            {
                string documentation = GenerateDocumentation();
                string filePath = SaveDocumentation(documentation);
                
                ConversionLogger.LogInfo($"参数文档生成成功: {filePath}");
                EditorUtility.DisplayDialog("文档生成完成", $"参数文档已生成到:\n{filePath}", "确定");
                
                // 打开文档文件
                System.Diagnostics.Process.Start(filePath);
            }
            catch (Exception ex)
            {
                ConversionLogger.LogError($"生成参数文档时发生错误: {ex.Message}");
                EditorUtility.DisplayDialog("生成失败", $"生成参数文档时发生错误:\n{ex.Message}", "确定");
            }
        }
        
        #endregion
        
        #region 文档生成
        
        /// <summary>
        /// 生成完整的参数文档
        /// </summary>
        private static string GenerateDocumentation()
        {
            var sb = new StringBuilder();
            
            // 文档头部
            sb.AppendLine("# ParticleSystem 参数参考文档");
            sb.AppendLine();
            sb.AppendLine("本文档列出了Unity ParticleSystem组件的所有可用属性，用于配置验证规则时参考。");
            sb.AppendLine();
            sb.AppendLine("## 生成信息");
            sb.AppendLine($"- 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"- Unity版本: {Application.unityVersion}");
            sb.AppendLine();
            
            // 各模块参数
            GenerateMainModuleDoc(sb);
            GenerateEmissionModuleDoc(sb);
            GenerateShapeModuleDoc(sb);
            GenerateVelocityOverLifetimeModuleDoc(sb);
            GenerateColorOverLifetimeModuleDoc(sb);
            GenerateSizeOverLifetimeModuleDoc(sb);
            GenerateRotationOverLifetimeModuleDoc(sb);
            GenerateRendererDoc(sb);
            GenerateOtherModulesDoc(sb);
            
            // 使用示例
            GenerateUsageExamples(sb);
            
            return sb.ToString();
        }
        
        /// <summary>
        /// 生成Main模块文档
        /// </summary>
        private static void GenerateMainModuleDoc(StringBuilder sb)
        {
            sb.AppendLine("## Main 模块");
            sb.AppendLine();
            sb.AppendLine("主要的粒子系统设置。");
            sb.AppendLine();
            sb.AppendLine("| 属性路径 | 类型 | 说明 | 示例值 |");
            sb.AppendLine("|---------|------|------|--------|");
            sb.AppendLine("| `main.duration` | float | 粒子系统持续时间 | `5.0` |");
            sb.AppendLine("| `main.loop` | bool | 是否循环播放 | `true` |");
            sb.AppendLine("| `main.prewarm` | bool | 是否预热 | `false` |");
            sb.AppendLine("| `main.startDelay` | MinMaxCurve | 开始延迟时间 | `0.0` |");
            sb.AppendLine("| `main.startLifetime` | MinMaxCurve | 粒子生命周期 | `5.0` |");
            sb.AppendLine("| `main.startSpeed` | MinMaxCurve | 初始速度 | `5.0` |");
            sb.AppendLine("| `main.startSize` | MinMaxCurve | 初始大小 | `1.0` |");
            sb.AppendLine("| `main.startRotation` | MinMaxCurve | 初始旋转 | `0.0` |");
            sb.AppendLine("| `main.startColor` | MinMaxGradient | 初始颜色 | `Color.white` |");
            sb.AppendLine("| `main.gravityModifier` | MinMaxCurve | 重力修正系数 | `0.0` |");
            sb.AppendLine("| `main.simulationSpace` | enum | 模拟空间 | `Local` |");
            sb.AppendLine("| `main.maxParticles` | int | 最大粒子数 | `1000` |");
            sb.AppendLine();
        }
        
        /// <summary>
        /// 生成Emission模块文档
        /// </summary>
        private static void GenerateEmissionModuleDoc(StringBuilder sb)
        {
            sb.AppendLine("## Emission 模块");
            sb.AppendLine();
            sb.AppendLine("控制粒子发射的设置。");
            sb.AppendLine();
            sb.AppendLine("| 属性路径 | 类型 | 说明 | 示例值 |");
            sb.AppendLine("|---------|------|------|--------|");
            sb.AppendLine("| `emission.enabled` | bool | 是否启用发射模块 | `true` |");
            sb.AppendLine("| `emission.rateOverTime` | MinMaxCurve | 每秒发射粒子数 | `10.0` |");
            sb.AppendLine("| `emission.rateOverDistance` | MinMaxCurve | 每单位距离发射粒子数 | `0.0` |");
            sb.AppendLine();
        }
        
        /// <summary>
        /// 生成Shape模块文档
        /// </summary>
        private static void GenerateShapeModuleDoc(StringBuilder sb)
        {
            sb.AppendLine("## Shape 模块");
            sb.AppendLine();
            sb.AppendLine("控制粒子发射形状的设置。");
            sb.AppendLine();
            sb.AppendLine("| 属性路径 | 类型 | 说明 | 示例值 |");
            sb.AppendLine("|---------|------|------|--------|");
            sb.AppendLine("| `shape.enabled` | bool | 是否启用形状模块 | `true` |");
            sb.AppendLine("| `shape.shapeType` | enum | 形状类型 | `Cone` |");
            sb.AppendLine("| `shape.angle` | float | 锥形角度 | `25.0` |");
            sb.AppendLine("| `shape.radius` | float | 半径 | `1.0` |");
            sb.AppendLine();
        }
        
        /// <summary>
        /// 生成VelocityOverLifetime模块文档
        /// </summary>
        private static void GenerateVelocityOverLifetimeModuleDoc(StringBuilder sb)
        {
            sb.AppendLine("## Velocity over Lifetime 模块");
            sb.AppendLine();
            sb.AppendLine("控制粒子生命周期内速度变化的设置。");
            sb.AppendLine();
            sb.AppendLine("| 属性路径 | 类型 | 说明 | 示例值 |");
            sb.AppendLine("|---------|------|------|--------|");
            sb.AppendLine("| `velocityOverLifetime.enabled` | bool | 是否启用速度模块 | `false` |");
            sb.AppendLine("| `velocityOverLifetime.space` | enum | 坐标空间 | `Local` |");
            sb.AppendLine();
        }
        
        /// <summary>
        /// 生成ColorOverLifetime模块文档
        /// </summary>
        private static void GenerateColorOverLifetimeModuleDoc(StringBuilder sb)
        {
            sb.AppendLine("## Color over Lifetime 模块");
            sb.AppendLine();
            sb.AppendLine("控制粒子生命周期内颜色变化的设置。");
            sb.AppendLine();
            sb.AppendLine("| 属性路径 | 类型 | 说明 | 示例值 |");
            sb.AppendLine("|---------|------|------|--------|");
            sb.AppendLine("| `colorOverLifetime.enabled` | bool | 是否启用颜色模块 | `false` |");
            sb.AppendLine("| `colorOverLifetime.color` | MinMaxGradient | 颜色渐变 | `Gradient` |");
            sb.AppendLine();
        }
        
        /// <summary>
        /// 生成SizeOverLifetime模块文档
        /// </summary>
        private static void GenerateSizeOverLifetimeModuleDoc(StringBuilder sb)
        {
            sb.AppendLine("## Size over Lifetime 模块");
            sb.AppendLine();
            sb.AppendLine("控制粒子生命周期内大小变化的设置。");
            sb.AppendLine();
            sb.AppendLine("| 属性路径 | 类型 | 说明 | 示例值 |");
            sb.AppendLine("|---------|------|------|--------|");
            sb.AppendLine("| `sizeOverLifetime.enabled` | bool | 是否启用大小模块 | `false` |");
            sb.AppendLine("| `sizeOverLifetime.separateAxes` | bool | 是否分轴缩放 | `false` |");
            sb.AppendLine("| `sizeOverLifetime.size` | MinMaxCurve | 大小曲线 | `1.0` |");
            sb.AppendLine();
        }
        
        /// <summary>
        /// 生成RotationOverLifetime模块文档
        /// </summary>
        private static void GenerateRotationOverLifetimeModuleDoc(StringBuilder sb)
        {
            sb.AppendLine("## Rotation over Lifetime 模块");
            sb.AppendLine();
            sb.AppendLine("控制粒子生命周期内旋转变化的设置。");
            sb.AppendLine();
            sb.AppendLine("| 属性路径 | 类型 | 说明 | 示例值 |");
            sb.AppendLine("|---------|------|------|--------|");
            sb.AppendLine("| `rotationOverLifetime.enabled` | bool | 是否启用旋转模块 | `false` |");
            sb.AppendLine("| `rotationOverLifetime.separateAxes` | bool | 是否分轴旋转 | `false` |");
            sb.AppendLine("| `rotationOverLifetime.z` | MinMaxCurve | Z轴旋转速度 | `0.0` |");
            sb.AppendLine();
        }
        
        /// <summary>
        /// 生成Renderer文档
        /// </summary>
        private static void GenerateRendererDoc(StringBuilder sb)
        {
            sb.AppendLine("## Renderer 组件");
            sb.AppendLine();
            sb.AppendLine("控制粒子渲染的设置。");
            sb.AppendLine();
            sb.AppendLine("| 属性路径 | 类型 | 说明 | 示例值 |");
            sb.AppendLine("|---------|------|------|--------|");
            sb.AppendLine("| `renderer.renderMode` | int | 渲染模式 (0=Billboard, 1=Stretch, 2=HorizontalBillboard, 3=VerticalBillboard, 4=Mesh) | `4` |");
            sb.AppendLine("| `renderer.mesh` | string | 网格名称 | `\"Quad\"` |");
            sb.AppendLine("| `renderer.material` | string | 材质名称 | `\"Default-Material\"` |");
            sb.AppendLine("| `renderer.sortingOrder` | int | 排序顺序 | `0` |");
            sb.AppendLine();
        }

        /// <summary>
        /// 生成其他模块文档
        /// </summary>
        private static void GenerateOtherModulesDoc(StringBuilder sb)
        {
            sb.AppendLine("## 其他模块");
            sb.AppendLine();
            sb.AppendLine("其他可用的粒子系统模块。");
            sb.AppendLine();
            sb.AppendLine("| 属性路径 | 类型 | 说明 | 示例值 |");
            sb.AppendLine("|---------|------|------|--------|");
            sb.AppendLine("| `limitVelocityOverLifetime.enabled` | bool | 限制速度模块 | `false` |");
            sb.AppendLine("| `inheritVelocity.enabled` | bool | 继承速度模块 | `false` |");
            sb.AppendLine("| `forceOverLifetime.enabled` | bool | 力场模块 | `false` |");
            sb.AppendLine("| `colorBySpeed.enabled` | bool | 速度颜色模块 | `false` |");
            sb.AppendLine("| `sizeBySpeed.enabled` | bool | 速度大小模块 | `false` |");
            sb.AppendLine("| `rotationBySpeed.enabled` | bool | 速度旋转模块 | `false` |");
            sb.AppendLine("| `externalForces.enabled` | bool | 外力模块 | `false` |");
            sb.AppendLine("| `noise.enabled` | bool | 噪声模块 | `false` |");
            sb.AppendLine("| `collision.enabled` | bool | 碰撞模块 | `false` |");
            sb.AppendLine("| `trigger.enabled` | bool | 触发器模块 | `false` |");
            sb.AppendLine("| `subEmitters.enabled` | bool | 子发射器模块 | `false` |");
            sb.AppendLine("| `textureSheetAnimation.enabled` | bool | 纹理表动画模块 | `false` |");
            sb.AppendLine("| `lights.enabled` | bool | 光照模块 | `false` |");
            sb.AppendLine("| `trails.enabled` | bool | 拖尾模块 | `false` |");
            sb.AppendLine();
        }

        /// <summary>
        /// 生成使用示例
        /// </summary>
        private static void GenerateUsageExamples(StringBuilder sb)
        {
            sb.AppendLine("## 使用示例");
            sb.AppendLine();
            sb.AppendLine("以下是一些常用的验证表达式示例：");
            sb.AppendLine();
            sb.AppendLine("### 基础验证");
            sb.AppendLine("```yaml");
            sb.AppendLine("# 检查最大粒子数为1");
            sb.AppendLine("- name: \"检查粒子数\"");
            sb.AppendLine("  expression: \"main.maxParticles == 1\"");
            sb.AppendLine("  severity: \"Error\"");
            sb.AppendLine("  errorMessage: \"粒子系统的最大粒子数必须为1\"");
            sb.AppendLine();
            sb.AppendLine("# 检查渲染模式为Mesh");
            sb.AppendLine("- name: \"检查渲染模式\"");
            sb.AppendLine("  expression: \"renderer.renderMode == 4\"");
            sb.AppendLine("  severity: \"Error\"");
            sb.AppendLine("  errorMessage: \"渲染模式必须为Mesh\"");
            sb.AppendLine("```");
            sb.AppendLine();
            sb.AppendLine("### 模块状态验证");
            sb.AppendLine("```yaml");
            sb.AppendLine("# 检查Shape模块未启用");
            sb.AppendLine("- name: \"检查Shape模块\"");
            sb.AppendLine("  expression: \"!shape.enabled\"");
            sb.AppendLine("  severity: \"Error\"");
            sb.AppendLine("  errorMessage: \"Shape模块必须禁用\"");
            sb.AppendLine();
            sb.AppendLine("# 检查多个模块都未启用");
            sb.AppendLine("- name: \"检查禁用模块\"");
            sb.AppendLine("  expression: \"!shape.enabled && !velocityOverLifetime.enabled && !noise.enabled\"");
            sb.AppendLine("  severity: \"Error\"");
            sb.AppendLine("  errorMessage: \"指定的模块必须全部禁用\"");
            sb.AppendLine("```");
            sb.AppendLine();
            sb.AppendLine("### 复合条件验证");
            sb.AppendLine("```yaml");
            sb.AppendLine("# 检查网格渲染模式且网格不为空");
            sb.AppendLine("- name: \"检查网格设置\"");
            sb.AppendLine("  expression: \"renderer.renderMode == 4 && renderer.mesh != null\"");
            sb.AppendLine("  severity: \"Error\"");
            sb.AppendLine("  errorMessage: \"必须使用Mesh渲染模式且网格不能为空\"");
            sb.AppendLine("```");
            sb.AppendLine();
            sb.AppendLine("## 注意事项");
            sb.AppendLine();
            sb.AppendLine("1. **属性路径**: 使用点号分隔的路径来访问嵌套属性");
            sb.AppendLine("2. **数据类型**: 注意属性的数据类型，bool值使用true/false，数值直接比较");
            sb.AppendLine("3. **枚举值**: 渲染模式等枚举属性使用整数值进行比较");
            sb.AppendLine("4. **空值检查**: 使用 `!= null` 检查对象是否存在");
            sb.AppendLine("5. **逻辑运算**: 支持 `&&`(与)、`||`(或)、`!`(非) 等逻辑运算符");
            sb.AppendLine();
        }

        /// <summary>
        /// 保存文档到文件
        /// </summary>
        private static string SaveDocumentation(string content)
        {
            string projectPath = Application.dataPath;
            string documentationFolder = Path.Combine(projectPath, "Editor", "Outsourced", "ParticleSystemToGfxFramework");
            string filePath = Path.Combine(documentationFolder, "ParticleSystem参数参考文档.md");

            // 确保目录存在
            Directory.CreateDirectory(documentationFolder);

            // 写入文件
            File.WriteAllText(filePath, content, Encoding.UTF8);

            // 刷新AssetDatabase
            AssetDatabase.Refresh();

            return filePath;
        }

        #endregion
    }
}
