#!/bin/bash

# 路径配置
EDITOR_LOG="/c/Users/<USER>/AppData/Local/Unity/Editor/Editor.log"

# 检查 Editor.log 是否存在
if [ ! -f "$EDITOR_LOG" ]; then
    echo "错误: 找不到 Editor.log 文件: $EDITOR_LOG"
    echo "请确认 Unity 已启动，或者更新脚本中的 EDITOR_LOG 路径。"
    exit 1
fi

# 在 WSL 中切换到 Windows 的 Unity 窗口
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 切换到 Unity 窗口..."

# 使用 PowerShell 调用 Windows API 来切换窗口
powershell.exe -Command "
Add-Type -TypeDefinition @'
using System;
using System.Runtime.InteropServices;

public class Win32 {
    [DllImport(\"user32.dll\", CharSet = CharSet.Auto)]
    public static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

    [DllImport(\"user32.dll\")]
    public static extern bool SetForegroundWindow(IntPtr hWnd);

    [DllImport(\"user32.dll\")]
    public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

    [DllImport(\"user32.dll\")]
    public static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);

    public delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

    [DllImport(\"user32.dll\", CharSet = CharSet.Auto)]
    public static extern int GetWindowText(IntPtr hWnd, System.Text.StringBuilder lpString, int nMaxCount);

    public const int SW_MINIMIZE = 6;
    public const int SW_RESTORE = 9;
}
'@

# 查找并激活 Unity 窗口
\$windowFound = \$false
\$callback = {
    param(\$hwnd, \$lparam)
    \$sb = New-Object System.Text.StringBuilder 256
    [Win32]::GetWindowText(\$hwnd, \$sb, \$sb.Capacity) | Out-Null
    \$title = \$sb.ToString()

    if (\$title -like \"*Unity*\") {
        # 先最小化窗口（强制改变状态）
        [Win32]::ShowWindow(\$hwnd, [Win32]::SW_MINIMIZE)
        # 恢复窗口并激活焦点
        [Win32]::ShowWindow(\$hwnd, [Win32]::SW_RESTORE)
        [Win32]::SetForegroundWindow(\$hwnd)
        \$script:windowFound = \$true
        return \$false  # 停止枚举
    }
    return \$true  # 继续枚举
}

\$delegate = [Win32+EnumWindowsProc]\$callback
[Win32]::EnumWindows(\$delegate, [IntPtr]::Zero)

if (\$windowFound) {
    Write-Host \"已切换到 Unity 窗口\" -ForegroundColor Green
} else {
    Write-Host \"未找到 Unity 窗口\" -ForegroundColor Red
}
"

# 等待Unity编译完成（5秒）
echo "等待Unity编译 (5秒)..."
sleep 5

# 读取编译日志的最新100行，筛选出警告和错误
echo "—— 编译结果 ——"
echo "读取 Editor.log 中的警告和错误..."

# 使用 tail 命令读取最新100行，并筛选出包含": warning "和": error "的行
tail -n 100 "$EDITOR_LOG" | grep -E ": warning |: error " | while read -r line; do
    if [[ $line == *": error "* ]]; then
        echo -e "\e[31m$line\e[0m"  # 红色显示错误
    elif [[ $line == *": warning "* ]]; then
        echo -e "\e[33m$line\e[0m"  # 黄色显示警告
    else
        echo "$line"
    fi
done

# 检查是否有警告或错误
if [ $(tail -n 100 "$EDITOR_LOG" | grep -c -E ": warning |: error ") -eq 0 ]; then
    echo -e "\e[32m没有发现警告或错误\e[0m"
fi
