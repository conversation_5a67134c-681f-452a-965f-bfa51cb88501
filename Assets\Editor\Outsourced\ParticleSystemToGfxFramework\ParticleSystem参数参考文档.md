﻿# ParticleSystem 参数参考文档

本文档列出了Unity ParticleSystem组件的所有可用属性，用于配置验证规则时参考。

## 生成信息
- 生成时间: 2025-06-06 18:37:54
- Unity版本: 2018.4.25f1

## Main 模块

主要的粒子系统设置。

| 属性路径 | 类型 | 说明 | 示例值 |
|---------|------|------|--------|
| `main.duration` | float | 粒子系统持续时间 | `5.0` |
| `main.loop` | bool | 是否循环播放 | `true` |
| `main.prewarm` | bool | 是否预热 | `false` |
| `main.startDelay` | MinMaxCurve | 开始延迟时间 | `0.0` |
| `main.startLifetime` | MinMaxCurve | 粒子生命周期 | `5.0` |
| `main.startSpeed` | MinMaxCurve | 初始速度 | `5.0` |
| `main.startSize` | MinMaxCurve | 初始大小 | `1.0` |
| `main.startRotation` | MinMaxCurve | 初始旋转 | `0.0` |
| `main.startColor` | MinMaxGradient | 初始颜色 | `Color.white` |
| `main.gravityModifier` | MinMaxCurve | 重力修正系数 | `0.0` |
| `main.simulationSpace` | enum | 模拟空间 | `Local` |
| `main.maxParticles` | int | 最大粒子数 | `1000` |

## Emission 模块

控制粒子发射的设置。

| 属性路径 | 类型 | 说明 | 示例值 |
|---------|------|------|--------|
| `emission.enabled` | bool | 是否启用发射模块 | `true` |
| `emission.rateOverTime` | MinMaxCurve | 每秒发射粒子数 | `10.0` |
| `emission.rateOverDistance` | MinMaxCurve | 每单位距离发射粒子数 | `0.0` |

## Shape 模块

控制粒子发射形状的设置。

| 属性路径 | 类型 | 说明 | 示例值 |
|---------|------|------|--------|
| `shape.enabled` | bool | 是否启用形状模块 | `true` |
| `shape.shapeType` | enum | 形状类型 | `Cone` |
| `shape.angle` | float | 锥形角度 | `25.0` |
| `shape.radius` | float | 半径 | `1.0` |

## Velocity over Lifetime 模块

控制粒子生命周期内速度变化的设置。

| 属性路径 | 类型 | 说明 | 示例值 |
|---------|------|------|--------|
| `velocityOverLifetime.enabled` | bool | 是否启用速度模块 | `false` |
| `velocityOverLifetime.space` | enum | 坐标空间 | `Local` |

## Color over Lifetime 模块

控制粒子生命周期内颜色变化的设置。

| 属性路径 | 类型 | 说明 | 示例值 |
|---------|------|------|--------|
| `colorOverLifetime.enabled` | bool | 是否启用颜色模块 | `false` |
| `colorOverLifetime.color` | MinMaxGradient | 颜色渐变 | `Gradient` |

## Size over Lifetime 模块

控制粒子生命周期内大小变化的设置。

| 属性路径 | 类型 | 说明 | 示例值 |
|---------|------|------|--------|
| `sizeOverLifetime.enabled` | bool | 是否启用大小模块 | `false` |
| `sizeOverLifetime.separateAxes` | bool | 是否分轴缩放 | `false` |
| `sizeOverLifetime.size` | MinMaxCurve | 大小曲线 | `1.0` |

## Rotation over Lifetime 模块

控制粒子生命周期内旋转变化的设置。

| 属性路径 | 类型 | 说明 | 示例值 |
|---------|------|------|--------|
| `rotationOverLifetime.enabled` | bool | 是否启用旋转模块 | `false` |
| `rotationOverLifetime.separateAxes` | bool | 是否分轴旋转 | `false` |
| `rotationOverLifetime.z` | MinMaxCurve | Z轴旋转速度 | `0.0` |

## Renderer 组件

控制粒子渲染的设置。

| 属性路径 | 类型 | 说明 | 示例值 |
|---------|------|------|--------|
| `renderer.renderMode` | int | 渲染模式 (0=Billboard, 1=Stretch, 2=HorizontalBillboard, 3=VerticalBillboard, 4=Mesh) | `4` |
| `renderer.mesh` | string | 网格名称 | `"Quad"` |
| `renderer.material` | string | 材质名称 | `"Default-Material"` |
| `renderer.sortingOrder` | int | 排序顺序 | `0` |

## 其他模块

其他可用的粒子系统模块。

| 属性路径 | 类型 | 说明 | 示例值 |
|---------|------|------|--------|
| `limitVelocityOverLifetime.enabled` | bool | 限制速度模块 | `false` |
| `inheritVelocity.enabled` | bool | 继承速度模块 | `false` |
| `forceOverLifetime.enabled` | bool | 力场模块 | `false` |
| `colorBySpeed.enabled` | bool | 速度颜色模块 | `false` |
| `sizeBySpeed.enabled` | bool | 速度大小模块 | `false` |
| `rotationBySpeed.enabled` | bool | 速度旋转模块 | `false` |
| `externalForces.enabled` | bool | 外力模块 | `false` |
| `noise.enabled` | bool | 噪声模块 | `false` |
| `collision.enabled` | bool | 碰撞模块 | `false` |
| `trigger.enabled` | bool | 触发器模块 | `false` |
| `subEmitters.enabled` | bool | 子发射器模块 | `false` |
| `textureSheetAnimation.enabled` | bool | 纹理表动画模块 | `false` |
| `lights.enabled` | bool | 光照模块 | `false` |
| `trails.enabled` | bool | 拖尾模块 | `false` |

## 使用示例

以下是一些常用的验证表达式示例：

### 基础验证
```yaml
# 检查最大粒子数为1
- name: "检查粒子数"
  expression: "main.maxParticles == 1"
  severity: "Error"
  errorMessage: "粒子系统的最大粒子数必须为1"

# 检查渲染模式为Mesh
- name: "检查渲染模式"
  expression: "renderer.renderMode == 4"
  severity: "Error"
  errorMessage: "渲染模式必须为Mesh"
```

### 模块状态验证
```yaml
# 检查Shape模块未启用
- name: "检查Shape模块"
  expression: "!shape.enabled"
  severity: "Error"
  errorMessage: "Shape模块必须禁用"

# 检查多个模块都未启用
- name: "检查禁用模块"
  expression: "!shape.enabled && !velocityOverLifetime.enabled && !noise.enabled"
  severity: "Error"
  errorMessage: "指定的模块必须全部禁用"
```

### 复合条件验证
```yaml
# 检查网格渲染模式且网格不为空
- name: "检查网格设置"
  expression: "renderer.renderMode == 4 && renderer.mesh != null"
  severity: "Error"
  errorMessage: "必须使用Mesh渲染模式且网格不能为空"
```

## 注意事项

1. **属性路径**: 使用点号分隔的路径来访问嵌套属性
2. **数据类型**: 注意属性的数据类型，bool值使用true/false，数值直接比较
3. **枚举值**: 渲染模式等枚举属性使用整数值进行比较
4. **空值检查**: 使用 `!= null` 检查对象是否存在
5. **逻辑运算**: 支持 `&&`(与)、`||`(或)、`!`(非) 等逻辑运算符

